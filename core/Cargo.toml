[package]
name = "defuse-core"
edition.workspace = true
version.workspace = true
rust-version.workspace = true
repository.workspace = true

[dependencies]
defuse-auth-call.workspace = true
defuse-bitmap.workspace = true
defuse-crypto = { workspace = true, features = ["serde"] }
defuse-erc191.workspace = true
defuse-nep245.workspace = true
defuse-nep413.workspace = true
defuse-map-utils.workspace = true
defuse-near-utils = { workspace = true, features = ["time"] }
defuse-num-utils.workspace = true
defuse-serde-utils.workspace = true
defuse-ton-connect.workspace = true
defuse-sep53.workspace = true
defuse-tip191.workspace = true
defuse-webauthn.workspace = true

arbitrary = { workspace = true, optional = true }
arbitrary_with = { workspace = true, optional = true }
chrono = { workspace = true, features = ["serde"] }
derive_more = { workspace = true, features = ["from"] }
hex.workspace = true
impl-tools.workspace = true
near-account-id.workspace = true
near-contract-standards.workspace = true
near-sdk.workspace = true
serde_with.workspace = true
strum.workspace = true
thiserror.workspace = true

[features]
abi = [
    "defuse-crypto/abi",
    "defuse-erc191/abi",
    "defuse-nep413/abi",
    "defuse-sep53/abi",
    "defuse-tip191/abi",

    "defuse-serde-utils/abi",
    "defuse-ton-connect/abi",
    "defuse-webauthn/abi",
]
arbitrary = [
    "dep:arbitrary",
    "dep:arbitrary_with",
    "defuse-bitmap/arbitrary",
    "defuse-crypto/arbitrary",
    "defuse-near-utils/arbitrary",
]

[target.'cfg(not(target_arch = "wasm32"))'.dependencies]
chrono = { workspace = true, features = ["now"] }

[dev-dependencies]
defuse-test-utils.workspace = true
defuse-near-utils = { workspace = true, features = ["arbitrary"] }

arbitrary.workspace = true
arbitrary_with.workspace = true
hex-literal.workspace = true
itertools.workspace = true
near-sdk = { workspace = true, features = ["unit-testing"] }
rstest.workspace = true

[lints]
workspace = true
