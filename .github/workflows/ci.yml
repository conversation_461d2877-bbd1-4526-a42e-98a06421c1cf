name: CI

on:
  push:
    branches:
      - main
      - develop
  pull_request:

jobs:
  fmt:
    name: Format
    runs-on: ubuntu-latest
    steps:
      - name: Checkout sources
        uses: actions/checkout@v4
      - name: Install Rust
        uses: actions-rust-lang/setup-rust-toolchain@v1
      - name: Rustfmt Check
        uses: actions-rust-lang/rustfmt@v1

  check:
    name: Check
    runs-on: ubuntu-latest
    needs: fmt
    steps:
      - name: Install Dependencies
        uses: awalsh128/cache-apt-pkgs-action@v1
        with:
          packages: libudev-dev # dependency for cargo-near
          version: 1.0
      - name: Checkout sources
        uses: actions/checkout@v4
      - name: Install Rust
        uses: actions-rust-lang/setup-rust-toolchain@v1
        with:
          # this action doesn't allow passing `shared-key` to Swatinem/rust-cache,
          # so we setup cache manually in the next step
          cache: false
      - name: Setup Rust cache
        uses: Swatinem/rust-cache@v2
        with:
          save-if: ${{ github.ref == 'refs/heads/main' }}
          cache-all-crates: "true" # Yes, it's a string
      - name: Install cargo-make
        run: cargo install cargo-make
      - name: Run clippy
        run: cargo make clippy

  build:
    name: Build
    runs-on: ubuntu-latest
    needs: check
    steps:
      - name: Install Dependencies
        uses: awalsh128/cache-apt-pkgs-action@v1
        with:
          packages: libudev-dev # dependency for cargo-near
          version: 1.0
      - name: Checkout sources
        uses: actions/checkout@v4
      - name: Install Rust
        uses: actions-rust-lang/setup-rust-toolchain@v1
        with:
          # this action doesn't allow passing `shared-key` to Swatinem/rust-cache,
          # so we setup cache manually in the next step
          cache: false
      - name: Setup Rust cache
        uses: Swatinem/rust-cache@v2
        with:
          save-if: ${{ github.ref == 'refs/heads/main' }}
          cache-all-crates: "true" # Yes, it's a string
      - name: Install Cargo Plugins
        run: cargo install cargo-make cargo-near
      - name: Build
        run: cargo make build
      - name: Upload Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: res
          path: ./res/

  tests:
    name: Tests
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Install Dependencies
        uses: awalsh128/cache-apt-pkgs-action@v1
        with:
          packages: libudev-dev
          version: 1.0
      - name: Checkout sources
        uses: actions/checkout@v4
      - name: Install Rust
        uses: actions-rust-lang/setup-rust-toolchain@v1
        with:
          # this action doesn't allow passing `shared-key` to Swatinem/rust-cache,
          # so we setup cache manually in the next step
          cache: false
      - name: Setup Rust cache
        uses: Swatinem/rust-cache@v2
        with:
          save-if: ${{ github.ref == 'refs/heads/main' }}
          cache-all-crates: "true" # Yes, it's a string
      - name: Install cargo-make
        run: cargo install cargo-make
      - name: Download Artifacts
        uses: actions/download-artifact@v4
        with:
          name: res
          path: ./res
      - name: Run Tests
        run: cargo make run-tests -- --show-output

  security_audit_report:
    name: Security Audit - report
    runs-on: ubuntu-latest
    permissions:
      checks: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: rustsec/audit-check@v2.0.0
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

  security_audit_deny:
    name: Security Audit - deny
    runs-on: ubuntu-latest
    needs: check
    steps:
      - name: Checkout sources
        uses: actions/checkout@v4
      - name: Install Rust
        uses: actions-rust-lang/setup-rust-toolchain@v1
        with:
          # this action doesn't allow passing `shared-key` to Swatinem/rust-cache,
          # so we setup cache manually in the next step
          cache: false
      - name: Setup Rust cache
        uses: Swatinem/rust-cache@v2
        with:
          save-if: ${{ github.ref == 'refs/heads/main' }}
          cache-all-crates: "true" # Yes, it's a string
      - name: Install Cargo Plugins
        run: cargo install cargo-audit
      - name: Run security audit
        run: cargo audit --deny unsound --deny yanked
        # run: cargo audit --deny warnings  # Warnings include: unmaintained, unsound and yanked

  contract_analysis:
    name: Shared security analysis
    uses: aurora-is-near/.github/.github/workflows/security_analysis.yml@master
    secrets:
      DD_API_KEY: ${{ secrets.DD_API_KEY }}
