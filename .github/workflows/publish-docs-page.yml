# Simple workflow for deploying static content to GitHub Pages
name: Publish static cargo docs

on:
  # Runs on pushes targeting the default branch
  push:
    branches: ["main"]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
# However, do NOT cancel in-progress runs as we want to allow these production deployments to complete.
concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  # Single deploy job since we're just deploying
  build:
    name: Publish cargo docs
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Install Rust
        uses: actions-rust-lang/setup-rust-toolchain@v1
        with:
          cache: false
      - name: Setup Pages
        uses: actions/configure-pages@v5
      - name: Clean docs folder
        run: cargo clean --doc
      - name: Build docs
        run: cargo doc --workspace --no-deps --features near-sdk/non-contract-usage
      - name: Add redirect
        run: echo '<meta http-equiv="refresh" content="0;url=defuse/index.html">' > target/doc/index.html
      - name: Remove lock file
        run: rm -f target/doc/.lock
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          # Upload docs dir
          path: "target/doc"

  deploy:
    name: Deploy
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
