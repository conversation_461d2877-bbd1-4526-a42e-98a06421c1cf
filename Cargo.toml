[workspace]
resolver = "3"
members = [
    "admin-utils",
    "auth-call",
    "bitmap",
    "borsh-utils",
    "controller",
    "core",
    "crypto",
    "defuse",
    "erc191",
    "io-utils",
    "map-utils",
    "near-utils",
    "nep245",
    "nep413",
    "nep461",
    "num-utils",
    "webauthn",
    "poa-factory",
    "poa-token",
    "randomness",
    "sep53",
    "serde-utils",
    "tests",
    "test-utils",
    "tip191",
    "ton-connect",
    "wnear",
]
default-members = ["defuse"]

[workspace.package]
edition = "2024"
repository = "https://github.com/near/intents"
version = "0.1.0"
rust-version = "1.86.0"

[workspace.dependencies]
defuse-admin-utils.path = "admin-utils"
defuse-auth-call.path = "auth-call"
defuse-bitmap.path = "bitmap"
defuse-borsh-utils.path = "borsh-utils"
defuse-controller.path = "controller"
defuse-core.path = "core"
defuse-crypto.path = "crypto"
defuse.path = "defuse"
defuse-erc191.path = "erc191"
defuse-io-utils.path = "io-utils"
defuse-map-utils.path = "map-utils"
defuse-near-utils.path = "near-utils"
defuse-nep245.path = "nep245"
defuse-nep413.path = "nep413"
defuse-nep461.path = "nep461"
defuse-num-utils.path = "num-utils"
defuse-webauthn.path = "webauthn"
defuse-poa-factory.path = "poa-factory"
defuse-poa-token.path = "poa-token"
defuse-sep53.path = "sep53"
defuse-serde-utils.path = "serde-utils"
defuse-tip191.path = "tip191"
defuse-ton-connect.path = "ton-connect"
defuse-wnear.path = "wnear"

defuse-randomness.path = "randomness"
defuse-test-utils.path = "test-utils"

anyhow = "1"
arbitrary = "1"
arbitrary_with = "0.3"
array-util = "1"
bitflags = "2.9.1"
bnum = { version = "0.13", features = ["borsh"] }
chrono = { version = "0.4", default-features = false }
derive_more = "2.0"
digest = { version = "0.10", default-features = false }
ed25519-dalek = { version = "2.1", default-features = false }
hex = "0.4"
hex-literal = "1.0"
impl-tools = "0.11"
itertools = "0.14"
near-account-id = "1.1"
near-contract-standards = "5.15"
near-crypto = "0.30"
near-plugins = { git = "https://github.com/Near-One/near-plugins", tag = "v0.5.0" }
near-sdk = "5.15"
near-workspaces = "0.20"
p256 = { version = "0.13", default-features = false, features = ["ecdsa"] }
rand = "0.9"
rand_chacha = "0.9"
rstest = "0.25"
schemars = "0.8"
serde_json = "1"
serde_with = "3.9"
stellar-strkey = "0.0"
strum = { version = "0.27", features = ["derive"] }
thiserror = "2"
tlb-ton = { version = "0.5", default-features = false }
tokio = { version = "1.45", default-features = false }

[workspace.lints.clippy]
all = { level = "deny", priority = -1 }
pedantic = { level = "deny", priority = -1 }
nursery = { level = "deny", priority = -1 }
as_conversions = { level = "deny", priority = -1 }

module_name_repetitions = "allow"
missing_errors_doc = "allow"
missing_panics_doc = "allow"
must_use_candidate = "allow"
unreadable_literal = "allow"
similar_names = "allow"
too_long_first_doc_paragraph = "allow"

[profile.release]
codegen-units = 1
opt-level = 3
lto = true
debug = false
strip = "symbols"
panic = "abort"
overflow-checks = true
