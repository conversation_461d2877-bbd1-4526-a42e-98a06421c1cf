[package]
name = "defuse-tip191"
edition.workspace = true
repository.workspace = true
version.workspace = true
rust-version.workspace = true

[dependencies]
defuse-crypto = { workspace = true, features = ["serde"] }

impl-tools.workspace = true
near-sdk.workspace = true
serde_with.workspace = true

[dev-dependencies]
defuse-test-utils.workspace = true
hex-literal.workspace = true
near-sdk = { workspace = true, features = ["unit-testing"] }
rstest.workspace = true

[features]
abi = ["defuse-crypto/abi"]

[lints]
workspace = true
