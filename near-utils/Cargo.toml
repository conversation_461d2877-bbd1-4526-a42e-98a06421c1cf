[package]
name = "defuse-near-utils"
edition.workspace = true
version.workspace = true
rust-version.workspace = true
repository.workspace = true

[dependencies]
defuse-borsh-utils.workspace = true

arbitrary_with = { workspace = true, optional = true }
chrono = { workspace = true, optional = true }
digest = { workspace = true, optional = true }
impl-tools.workspace = true
hex = { workspace = true, optional = true }
near-account-id = { workspace = true, optional = true }
near-sdk.workspace = true

[features]
arbitrary = ["dep:arbitrary_with", "dep:hex", "dep:near-account-id"]
digest = ["dep:digest"]
time = ["dep:chrono"]

[dev-dependencies]
defuse-test-utils.workspace = true

rstest.workspace = true
near-sdk = { workspace = true, features = ["unit-testing"] }

[lints]
workspace = true
