use near_sdk::{Bors<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IntoStorageKey, borsh::BorshSerialize};

pub trait NestPrefix: Sized + IntoStorageKey {
    fn nest<S>(self, nested: S) -> NestedPrefix<Self, S>
    where
        S: BorshSerialize,
    {
        NestedPrefix {
            parent: self,
            nested,
        }
    }
}
impl<T> NestPrefix for T where T: IntoStorageKey {}

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, PartialOrd, Ord, BorshSerialize, BorshStorageKey)]
#[borsh(crate = "::near_sdk::borsh")]
pub struct NestedPrefix<S, P> {
    parent: S,
    nested: P,
}
