[package]
name = "defuse-serde-utils"
edition.workspace = true
version.workspace = true
rust-version.workspace = true
repository.workspace = true

[dependencies]
derive_more = { workspace = true, features = ["from"] }
near-sdk.workspace = true
serde_with.workspace = true
tlb-ton = { workspace = true, optional = true }

[features]
default = ["base58", "base64"]

abi = []
base58 = []
base64 = ["serde_with/base64"]
tlb = ["dep:tlb-ton"]

[lints]
workspace = true
