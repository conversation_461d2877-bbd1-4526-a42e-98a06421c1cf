[package]
name = "defuse-crypto"
edition.workspace = true
version.workspace = true
rust-version.workspace = true
repository.workspace = true

[dependencies]
arbitrary = { workspace = true, features = ["derive"], optional = true }
ed25519-dalek.workspace = true
hex.workspace = true
near-sdk = { workspace = true, features = ["unstable"] }
p256.workspace = true
serde_with = { workspace = true, optional = true }
strum.workspace = true
thiserror.workspace = true

[features]
abi = ["serde", "serde_with/schemars_0_8"]
arbitrary = ["dep:arbitrary"]
serde = ["dep:serde_with"]

[lints]
workspace = true
