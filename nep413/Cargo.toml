[package]
name = "defuse-nep413"
edition.workspace = true
version.workspace = true
rust-version.workspace = true
repository.workspace = true

[dependencies]
defuse-crypto = { workspace = true, features = ["serde"] }
defuse-near-utils.workspace = true
defuse-nep461.workspace = true
defuse-serde-utils.workspace = true

impl-tools.workspace = true
near-sdk.workspace = true
serde_with.workspace = true

[features]
abi = ["defuse-serde-utils/abi", "defuse-crypto/abi"]

[lints]
workspace = true
