[package]
name = "defuse-sep53"
edition.workspace = true
repository.workspace = true
version.workspace = true
rust-version.workspace = true

[dependencies]
defuse-crypto = { workspace = true, features = ["serde"] }

impl-tools.workspace = true
near-sdk.workspace = true
serde_with.workspace = true

[dev-dependencies]
defuse-test-utils.workspace = true
ed25519-dalek.workspace = true
near-crypto.workspace = true
rstest.workspace = true
stellar-strkey.workspace = true
near-sdk = { workspace = true, features = ["unit-testing"] }

[features]
abi = ["defuse-crypto/abi"]

[lints]
workspace = true
