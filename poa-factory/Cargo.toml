[package]
name = "defuse-poa-factory"
version = "0.1.0"
edition.workspace = true
rust-version.workspace = true
repository.workspace = true

[lib]
crate-type = ["cdylib", "rlib"]

[lints]
workspace = true

[dependencies]
defuse-admin-utils.workspace = true
defuse-near-utils.workspace = true
defuse-poa-token.workspace = true

near-contract-standards.workspace = true
near-plugins.workspace = true
near-sdk.workspace = true

[features]
contract = []
