[config]
default_to_workspace = false
skip_core_tasks = true

[env]
TARGET_DIR = "${CARGO_MAKE_WORKSPACE_WORKING_DIRECTORY}/res"
POA_TOKEN_WASM = "${TARGET_DIR}/defuse_poa_token.wasm"
POA_TOKEN_WITH_NO_REGISTRATION_DIR = "${TARGET_DIR}/poa-token-no-registration"
POA_TOKEN_WASM_NO_REGISTRATION_WASM = "${POA_TOKEN_WITH_NO_REGISTRATION_DIR}/defuse_poa_token.wasm"

[tasks.default]
alias = "build"

[tasks.clippy]
dependencies = ["add-cache-dir-tag"]
command = "cargo"
args = ["clippy", "--workspace", "--all-targets", "--no-deps"]

[tasks.build]
dependencies = [
    "add-cache-dir-tag",
    "build-defuse",
    "build-poa-factory",
    "contract-stats",
    "build-poa-token-no-registration",
]

[tasks.build-defuse]
dependencies = ["add-cache-dir-tag"]
command = "cargo"
args = [
    "near",
    "build",
    "non-reproducible-wasm",
    "--locked",
    "--manifest-path",
    "./defuse/Cargo.toml",
    "--features",
    "abi,contract",
    "--out-dir",
    "${TARGET_DIR}",
    "--no-embed-abi",
]

[tasks.build-poa-factory]
dependencies = ["add-cache-dir-tag", "build-poa-token"]
command = "cargo"
args = [
    "near",
    "build",
    "non-reproducible-wasm",
    "--locked",
    "--manifest-path",
    "./poa-factory/Cargo.toml",
    "--features",
    "contract",
    "--out-dir",
    "${TARGET_DIR}",
    "--no-embed-abi",
]

[tasks.build-poa-token]
dependencies = ["add-cache-dir-tag"]
command = "cargo"
args = [
    "near",
    "build",
    "non-reproducible-wasm",
    "--locked",
    "--manifest-path",
    "./poa-token/Cargo.toml",
    "--features",
    "contract",
    "--out-dir",
    "${TARGET_DIR}",
    "--no-embed-abi",
]

[tasks.build-poa-token-no-registration]
dependencies = ["add-cache-dir-tag"]
command = "cargo"
args = [
    "near",
    "build",
    "non-reproducible-wasm",
    "--locked",
    "--manifest-path",
    "./poa-token/Cargo.toml",
    "--features",
    "contract,no-registration",
    "--out-dir",
    "${POA_TOKEN_WITH_NO_REGISTRATION_DIR}",
    "--no-embed-abi",
]

[tasks.test]
alias = "tests"

[tasks.nextest]
dependencies = ["build"]
command = "cargo"
args = ["nextest", "run", "--all", "${@}"]

[tasks.tests]
dependencies = ["build"]
run_task = "run-tests"

[tasks.run-tests]
command = "cargo"
args = ["test", "--workspace", "--all-targets", "${@}"]

[tasks.clean]
dependencies = ["rm-contracts"]
command = "cargo"
args = ["clean"]

[tasks.rm-contracts]
script = "rm -rf ${TARGET_DIR}"

[tasks.contract-stats]
category = "Tools"
script = "cd ${TARGET_DIR} && du -ah *.wasm"

# We add CACHEDIR.TAG as it helps in making system level applications, e.g., backup systems, understand that this directory is just for disposable things.
[tasks.add-cache-dir-tag]
condition = { platforms = ["linux", "mac"] }
script = [
    "mkdir -p target res",
    "echo 'Signature: 8a477f597d28d172789f06886806bc55' | tee target/CACHEDIR.TAG res/CACHEDIR.TAG > /dev/null",
]
