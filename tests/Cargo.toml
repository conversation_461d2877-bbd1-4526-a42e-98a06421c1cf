[package]
name = "defuse-tests"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
repository.workspace = true

[lints]
workspace = true

[dev-dependencies]
defuse = { workspace = true, features = ["contract"] }
defuse-near-utils = { workspace = true, features = ["arbitrary"] }
defuse-poa-factory = { workspace = true, features = ["contract"] }
defuse-serde-utils = { workspace = true }
defuse-randomness.workspace = true
defuse-test-utils.workspace = true

anyhow.workspace = true
arbitrary.workspace = true
arbitrary_with = { workspace = true }
bnum = { workspace = true, features = ["rand"] }
chrono.workspace = true
derive_more.workspace = true
hex-literal.workspace = true
hex.workspace = true
impl-tools.workspace = true
near-crypto.workspace = true
near-sdk = { workspace = true, features = ["unit-testing"] }
near-workspaces.workspace = true
near-contract-standards.workspace = true
rstest.workspace = true
serde_json.workspace = true
strum.workspace = true
tokio = { workspace = true, features = ["macros"] }
tlb-ton = { workspace = true, features = ["arbitrary"] }
