use crate::{
    tests::defuse::{<PERSON><PERSON><PERSON><PERSON><PERSON>, SigningStandard, env::Env},
    utils::mt::MtExt,
};
use arbitrary::{Arbitrary, Unstructured};
use defuse::core::token_id::{TokenId, nep141::Nep141TokenId};
use defuse::core::{
    Deadline,
    fees::Pips,
    intents::{
        DefuseIntents,
        token_diff::{TokenDeltas, TokenDiff},
    },
    payload::multi::MultiPayload,
};
use defuse_randomness::{Rng, make_true_rng};
use defuse_test_utils::random::rng;
use near_sdk::AccountId;
use near_workspaces::Account;
use rstest::rstest;
use std::{collections::BTreeMap, time::Duration};

use super::ExecuteIntentsExt;

#[rstest]
#[tokio::test]
#[trace]
async fn swap_p2p(
    #[values(Pips::ZERO, Pips::ONE_BIP, Pips::ONE_PERCENT)] fee: Pips,
    #[values(false, true)] no_registration: bool,
) {
    use defuse::core::token_id::nep141::Nep141TokenId;

    let env = Env::builder()
        .fee(fee)
        .no_registration(no_registration)
        .build()
        .await;

    let ft1_token_id = TokenId::from(Nep141TokenId::new(env.ft1.clone()));

    let ft2_token_id = TokenId::from(Nep141TokenId::new(env.ft2.clone()));

    test_ft_diffs(
        &env,
        [
            AccountFtDiff {
                account: &env.user1,
                init_balances: std::iter::once((&env.ft1, 100)).collect(),
                diff: [TokenDeltas::default()
                    .with_apply_deltas([
                        (ft1_token_id.clone(), -100),
                        (
                            ft2_token_id.clone(),
                            TokenDiff::closure_delta(&ft2_token_id, -200, fee).unwrap(),
                        ),
                    ])
                    .unwrap()]
                .into(),
                result_balances: std::iter::once((
                    &env.ft2,
                    TokenDiff::closure_delta(&ft2_token_id, -200, fee).unwrap(),
                ))
                .collect(),
            },
            AccountFtDiff {
                account: &env.user2,
                init_balances: std::iter::once((&env.ft2, 200)).collect(),
                diff: [TokenDeltas::default()
                    .with_apply_deltas([
                        (
                            ft1_token_id.clone(),
                            TokenDiff::closure_delta(&ft1_token_id, -100, fee).unwrap(),
                        ),
                        (ft2_token_id.clone(), -200),
                    ])
                    .unwrap()]
                .into(),
                result_balances: std::iter::once((
                    &env.ft1,
                    TokenDiff::closure_delta(&ft1_token_id, -100, fee).unwrap(),
                ))
                .collect(),
            },
        ]
        .into(),
    )
    .await;
}

#[rstest]
#[tokio::test]
#[trace]
async fn swap_many(
    #[values(Pips::ZERO, Pips::ONE_BIP, Pips::ONE_PERCENT)] fee: Pips,
    #[values(false, true)] no_registration: bool,
) {
    let env = Env::builder()
        .fee(fee)
        .no_registration(no_registration)
        .build()
        .await;

    let ft1_token_id = TokenId::from(Nep141TokenId::new(env.ft1.clone()));
    let ft2_token_id = TokenId::from(Nep141TokenId::new(env.ft2.clone()));
    let ft3_token_id = TokenId::from(Nep141TokenId::new(env.ft3.clone()));

    test_ft_diffs(
        &env,
        [
            AccountFtDiff {
                account: &env.user1,
                init_balances: std::iter::once((&env.ft1, 100)).collect(),
                diff: [TokenDeltas::default()
                    .with_apply_deltas([(ft1_token_id.clone(), -100), (ft2_token_id.clone(), 200)])
                    .unwrap()]
                .into(),
                result_balances: std::iter::once((&env.ft2, 200)).collect(),
            },
            AccountFtDiff {
                account: &env.user2,
                init_balances: std::iter::once((&env.ft2, 1000)).collect(),
                diff: [
                    TokenDeltas::default()
                        .with_apply_deltas([
                            (
                                ft1_token_id.clone(),
                                TokenDiff::closure_delta(&ft1_token_id, -100, fee).unwrap(),
                            ),
                            (
                                ft2_token_id.clone(),
                                TokenDiff::closure_delta(&ft2_token_id, 200, fee).unwrap(),
                            ),
                        ])
                        .unwrap(),
                    TokenDeltas::default()
                        .with_apply_deltas([
                            (
                                ft2_token_id.clone(),
                                TokenDiff::closure_delta(&ft2_token_id, 300, fee).unwrap(),
                            ),
                            (
                                ft3_token_id.clone(),
                                TokenDiff::closure_delta(&ft3_token_id, -500, fee).unwrap(),
                            ),
                        ])
                        .unwrap(),
                ]
                .into(),
                result_balances: [
                    (
                        &env.ft1,
                        TokenDiff::closure_delta(&ft1_token_id, -100, fee).unwrap(),
                    ),
                    (
                        &env.ft2,
                        1000 + TokenDiff::closure_delta(&ft2_token_id, 200, fee).unwrap()
                            + TokenDiff::closure_delta(&ft2_token_id, 300, fee).unwrap(),
                    ),
                    (
                        &env.ft3,
                        TokenDiff::closure_delta(&ft3_token_id, -500, fee).unwrap(),
                    ),
                ]
                .into_iter()
                .collect(),
            },
            AccountFtDiff {
                account: &env.user3,
                init_balances: std::iter::once((&env.ft3, 500)).collect(),
                diff: [TokenDeltas::default()
                    .with_apply_deltas([(ft2_token_id.clone(), 300), (ft3_token_id.clone(), -500)])
                    .unwrap()]
                .into(),
                result_balances: std::iter::once((&env.ft2, 300)).collect(),
            },
        ]
        .into(),
    )
    .await;
}

type FtBalances<'a> = BTreeMap<&'a AccountId, i128>;

#[derive(Debug)]
struct AccountFtDiff<'a> {
    account: &'a Account,
    init_balances: FtBalances<'a>,
    diff: Vec<TokenDeltas>,
    result_balances: FtBalances<'a>,
}

async fn test_ft_diffs(env: &Env, accounts: Vec<AccountFtDiff<'_>>) {
    // deposit
    for account in &accounts {
        for (token_id, balance) in &account.init_balances {
            env.defuse_ft_deposit_to(
                token_id,
                (*balance).try_into().unwrap(),
                account.account.id(),
            )
            .await
            .unwrap();
        }
    }

    let signed: Vec<MultiPayload> = accounts
        .iter()
        .flat_map(move |account| {
            account.diff.iter().cloned().map(move |diff| {
                account.account.sign_defuse_message(
                    SigningStandard::default(),
                    env.defuse.id(),
                    make_true_rng().random(),
                    Deadline::timeout(Duration::from_secs(120)),
                    DefuseIntents {
                        intents: [TokenDiff {
                            diff,
                            memo: None,
                            referral: None,
                        }
                        .into()]
                        .into(),
                    },
                )
            })
        })
        .collect();

    // simulate
    env.defuse
        .simulate_intents(signed.clone())
        .await
        .unwrap()
        .into_result()
        .unwrap();

    // verify
    env.defuse.execute_intents(signed).await.unwrap();

    // check balances
    for account in accounts {
        let (tokens, balances): (Vec<_>, Vec<_>) = account
            .result_balances
            .into_iter()
            .map(|(t, b)| {
                (
                    TokenId::from(Nep141TokenId::new(t.clone())).to_string(),
                    u128::try_from(b).unwrap(),
                )
            })
            .unzip();
        assert_eq!(
            env.mt_contract_batch_balance_of(env.defuse.id(), account.account.id(), &tokens)
                .await
                .unwrap(),
            balances
        );
    }
}

#[tokio::test]
#[rstest]
#[trace]
async fn invariant_violated(
    #[notrace] mut rng: impl Rng,
    #[values(false, true)] no_registration: bool,
) {
    let env = Env::builder()
        .no_registration(no_registration)
        .build()
        .await;

    let ft1 = TokenId::from(Nep141TokenId::new(env.ft1.clone()));
    let ft2 = TokenId::from(Nep141TokenId::new(env.ft2.clone()));

    // deposit
    env.defuse_ft_deposit_to(&env.ft1, 1000, env.user1.id())
        .await
        .unwrap();
    env.defuse_ft_deposit_to(&env.ft2, 2000, env.user2.id())
        .await
        .unwrap();

    let nonce1 = rng.random();
    let nonce2 = rng.random();

    let signed: Vec<_> = [
        env.user1.sign_defuse_message(
            SigningStandard::arbitrary(&mut Unstructured::new(&rng.random::<[u8; 1]>())).unwrap(),
            env.defuse.id(),
            nonce1,
            Deadline::MAX,
            DefuseIntents {
                intents: [TokenDiff {
                    diff: TokenDeltas::default()
                        .with_apply_deltas([(ft1.clone(), -1000), (ft2.clone(), 2000)])
                        .unwrap(),
                    memo: None,
                    referral: None,
                }
                .into()]
                .into(),
            },
        ),
        env.user1.sign_defuse_message(
            SigningStandard::arbitrary(&mut Unstructured::new(&rng.random::<[u8; 1]>())).unwrap(),
            env.defuse.id(),
            nonce2,
            Deadline::MAX,
            DefuseIntents {
                intents: [TokenDiff {
                    diff: TokenDeltas::default()
                        .with_apply_deltas([(ft1.clone(), 1000), (ft2.clone(), -1999)])
                        .unwrap(),
                    memo: None,
                    referral: None,
                }
                .into()]
                .into(),
            },
        ),
    ]
    .into();

    assert_eq!(
        env.defuse
            .simulate_intents(signed.clone())
            .await
            .unwrap()
            .invariant_violated
            .unwrap()
            .into_unmatched_deltas(),
        Some(TokenDeltas::new(
            std::iter::once((ft2.clone(), 1)).collect()
        ))
    );

    env.defuse.execute_intents(signed).await.unwrap_err();

    // balances should stay the same
    assert_eq!(
        env.mt_contract_batch_balance_of(
            env.defuse.id(),
            env.user1.id(),
            [&ft1.to_string(), &ft2.to_string()]
        )
        .await
        .unwrap(),
        [1000, 0]
    );
    assert_eq!(
        env.mt_contract_batch_balance_of(
            env.defuse.id(),
            env.user2.id(),
            [&ft1.to_string(), &ft2.to_string()]
        )
        .await
        .unwrap(),
        [0, 2000]
    );
}

#[rstest]
#[tokio::test]
#[trace]
async fn solver_user_closure(
    #[notrace] mut rng: impl Rng,
    #[values(Pips::ZERO, Pips::ONE_BIP, Pips::ONE_PERCENT)] fee: Pips,
    #[values(false, true)] no_registration: bool,
) {
    const USER_BALANCE: u128 = 1100;
    const SOLVER_BALANCE: u128 = 2100;

    // RFQ: 1000 token_in -> ??? token_out
    const USER_DELTA_IN: i128 = -1000;

    let env = Env::builder()
        .fee(fee)
        .no_registration(no_registration)
        .build()
        .await;

    let user = &env.user1;
    let solver = &env.user2;

    // deposit
    env.defuse_ft_deposit_to(&env.ft1, USER_BALANCE, user.id())
        .await
        .unwrap();
    env.defuse_ft_deposit_to(&env.ft2, SOLVER_BALANCE, solver.id())
        .await
        .unwrap();

    let token_in = TokenId::from(Nep141TokenId::new(env.ft1.clone()));
    let token_out = TokenId::from(Nep141TokenId::new(env.ft2.clone()));

    dbg!(USER_DELTA_IN);
    // propagate RFQ to solver with adjusted amount_in
    let solver_delta_in = TokenDiff::closure_delta(&token_in, USER_DELTA_IN, fee).unwrap();

    // assume solver trades 1:2
    let solver_delta_out = solver_delta_in * -2;
    dbg!(solver_delta_in, solver_delta_out);

    let nonce = rng.random();

    // solver signs his intent
    let solver_commitment = solver.sign_defuse_message(
        SigningStandard::arbitrary(&mut Unstructured::new(&rng.random::<[u8; 1]>())).unwrap(),
        env.defuse.id(),
        nonce,
        Deadline::timeout(Duration::from_secs(90)),
        DefuseIntents {
            intents: [TokenDiff {
                diff: TokenDeltas::new(
                    [
                        (token_in.clone(), solver_delta_in),
                        (token_out.clone(), solver_delta_out),
                    ]
                    .into_iter()
                    .collect(),
                ),
                memo: None,
                referral: None,
            }
            .into()]
            .into(),
        },
    );

    // simulate before returning quote
    let simulation_before_return_quote = env
        .defuse
        .simulate_intents([solver_commitment.clone()])
        .await
        .unwrap();
    println!(
        "simulation_before_return_quote: {}",
        serde_json::to_string_pretty(&simulation_before_return_quote).unwrap()
    );

    // we expect unmatched deltas to correspond with user_delta_in and
    // user_delta_out and fee
    let unmatched_deltas = simulation_before_return_quote
        .invariant_violated
        .unwrap()
        .into_unmatched_deltas()
        .unwrap();
    // there should be unmatched deltas only for 2 tokens: token_in and token_out
    assert_eq!(unmatched_deltas.len(), 2);

    // expect unmatched delta on token_in to be fully covered by user_in
    let expected_unmatched_delta_token_in =
        TokenDiff::closure_delta(&token_in, USER_DELTA_IN, fee).unwrap();
    assert_eq!(
        unmatched_deltas.amount_for(&token_in),
        expected_unmatched_delta_token_in
    );

    // calculate user_delta_out to return to the user
    let user_delta_out =
        TokenDiff::closure_supply_delta(&token_out, unmatched_deltas.amount_for(&token_out), fee)
            .unwrap();
    dbg!(user_delta_out);

    let nonce = rng.random();

    // user signs the message
    let user_commitment = user.sign_defuse_message(
        SigningStandard::arbitrary(&mut Unstructured::new(&rng.random::<[u8; 1]>())).unwrap(),
        env.defuse.id(),
        nonce,
        Deadline::timeout(Duration::from_secs(90)),
        DefuseIntents {
            intents: [TokenDiff {
                diff: TokenDeltas::new(
                    [
                        (token_in.clone(), USER_DELTA_IN),
                        (token_out.clone(), user_delta_out),
                    ]
                    .into_iter()
                    .collect(),
                ),
                memo: None,
                referral: None,
            }
            .into()]
            .into(),
        },
    );

    // simulating both solver's and user's intents now should succeed
    env.defuse
        .simulate_intents([solver_commitment.clone(), user_commitment.clone()])
        .await
        .unwrap()
        .into_result()
        .unwrap();

    // execute intents
    env.defuse
        .execute_intents([solver_commitment, user_commitment])
        .await
        .unwrap();

    assert_eq!(
        env.mt_contract_batch_balance_of(
            env.defuse.id(),
            user.id(),
            [&token_in.to_string(), &token_out.to_string()]
        )
        .await
        .unwrap(),
        [
            USER_BALANCE - USER_DELTA_IN.unsigned_abs(),
            user_delta_out.unsigned_abs()
        ]
    );

    assert_eq!(
        env.mt_contract_batch_balance_of(
            env.defuse.id(),
            solver.id(),
            [&token_in.to_string(), &token_out.to_string()]
        )
        .await
        .unwrap(),
        [
            solver_delta_in.unsigned_abs(),
            SOLVER_BALANCE - solver_delta_out.unsigned_abs()
        ]
    );
}

/// Proof-of-concept test demonstrating fee leakage through consolidation and positive delta manipulation.
///
/// This test shows three key fee leakage mechanisms:
/// 1. Rounding discontinuity: Many small negative deltas vs one large negative delta
/// 2. Fee-basis leakage: Using positive deltas (fee-free) to reduce effective fee rate
/// 3. NEP-245 discontinuity: Single-unit transfers avoid fees entirely
#[rstest]
#[tokio::test]
#[trace]
async fn fee_leakage_poc(
    #[notrace] mut rng: impl Rng,
) {
    // Use a significant fee rate to make the leakage visible
    let fee = Pips::from_percent(1).unwrap(); // 1% = 10,000 pips

    let env = Env::builder()
        .fee(fee)
        .fee_collector("fee_collector.test".parse().unwrap())
        .build()
        .await;

    let token_id = TokenId::from(Nep141TokenId::new(env.ft1.clone()));

    // Get fee collector account ID
    let fee_collector_id: AccountId = env.defuse
        .view("fee_collector")
        .await
        .unwrap()
        .json()
        .unwrap();

    println!("Fee collector: {}", fee_collector_id);
    println!("Configured fee: {} ({}%)", fee.as_pips(), fee.as_f64() * 100.0);

    // === SCENARIO A: Baseline - Single large negative delta ===
    println!("\n=== SCENARIO A: Baseline - Single large negative delta ===");

    // Setup: User1 has 10,000 tokens, User2 will receive them
    const TOTAL_AMOUNT: u128 = 10_000;
    env.defuse_ft_deposit_to(&env.ft1, TOTAL_AMOUNT, env.user1.id())
        .await
        .unwrap();

    // Create balanced intents: user1 sends -10,000, user2 receives +10,000
    let user1_intent = env.user1.sign_defuse_message(
        SigningStandard::default(),
        env.defuse.id(),
        rng.random(),
        Deadline::timeout(Duration::from_secs(120)),
        DefuseIntents {
            intents: [TokenDiff {
                diff: TokenDeltas::new([(token_id.clone(), -10_000i128)].into_iter().collect()),
                memo: Some("baseline_single_large_negative".to_string()),
                referral: None,
            }.into()].into(),
        },
    );

    let user2_intent = env.user2.sign_defuse_message(
        SigningStandard::default(),
        env.defuse.id(),
        rng.random(),
        Deadline::timeout(Duration::from_secs(120)),
        DefuseIntents {
            intents: [TokenDiff {
                diff: TokenDeltas::new([(token_id.clone(), 10_000i128)].into_iter().collect()),
                memo: Some("baseline_single_large_positive".to_string()),
                referral: None,
            }.into()].into(),
        },
    );

    // Check fee collector balance before
    let fee_collector_balance_before_a = env.defuse
        .mt_balance_of(&fee_collector_id, &token_id.to_string())
        .await
        .unwrap();

    // Execute intents
    env.defuse.execute_intents([user1_intent, user2_intent]).await.unwrap();

    // Check fee collector balance after
    let fee_collector_balance_after_a = env.defuse
        .mt_balance_of(&fee_collector_id, &token_id.to_string())
        .await
        .unwrap();

    let baseline_fee_collected = fee_collector_balance_after_a - fee_collector_balance_before_a;
    let expected_baseline_fee = fee.fee_ceil(TOTAL_AMOUNT);

    println!("Baseline fee collected: {} tokens", baseline_fee_collected);
    println!("Expected baseline fee: {} tokens", expected_baseline_fee);
    assert_eq!(baseline_fee_collected, expected_baseline_fee);

    // === SCENARIO B: Fee leakage through consolidation ===
    println!("\n=== SCENARIO B: Fee leakage through consolidation ===");

    // Reset: Give user2 the same amount
    env.defuse_ft_deposit_to(&env.ft1, TOTAL_AMOUNT, env.user2.id())
        .await
        .unwrap();

    // Create 100 small negative deltas of 100 tokens each (total: 10,000)
    // Plus one large positive delta to balance
    const NUM_SMALL_DELTAS: usize = 100;
    const SMALL_DELTA_AMOUNT: i128 = 100;

    let mut small_deltas_intents = Vec::new();
    for i in 0..NUM_SMALL_DELTAS {
        let intent = env.user2.sign_defuse_message(
            SigningStandard::default(),
            env.defuse.id(),
            rng.random(),
            Deadline::timeout(Duration::from_secs(120)),
            DefuseIntents {
                intents: [TokenDiff {
                    diff: TokenDeltas::new([(token_id.clone(), -SMALL_DELTA_AMOUNT)].into_iter().collect()),
                    memo: Some(format!("small_negative_{}", i)),
                    referral: None,
                }.into()].into(),
            },
        );
        small_deltas_intents.push(intent);
    }

    // Add balancing positive delta from user3
    let balancing_intent = env.user3.sign_defuse_message(
        SigningStandard::default(),
        env.defuse.id(),
        rng.random(),
        Deadline::timeout(Duration::from_secs(120)),
        DefuseIntents {
            intents: [TokenDiff {
                diff: TokenDeltas::new([(token_id.clone(), TOTAL_AMOUNT as i128)].into_iter().collect()),
                memo: Some("balancing_positive".to_string()),
                referral: None,
            }.into()].into(),
        },
    );
    small_deltas_intents.push(balancing_intent);

    // Check fee collector balance before
    let fee_collector_balance_before_b = env.defuse
        .mt_balance_of(&fee_collector_id, &token_id.to_string())
        .await
        .unwrap();

    // Execute all small delta intents
    env.defuse.execute_intents(small_deltas_intents).await.unwrap();

    // Check fee collector balance after
    let fee_collector_balance_after_b = env.defuse
        .mt_balance_of(&fee_collector_id, &token_id.to_string())
        .await
        .unwrap();

    let small_deltas_fee_collected = fee_collector_balance_after_b - fee_collector_balance_before_b;
    let expected_small_deltas_fee = NUM_SMALL_DELTAS as u128 * fee.fee_ceil(SMALL_DELTA_AMOUNT as u128);

    println!("Small deltas fee collected: {} tokens", small_deltas_fee_collected);
    println!("Expected small deltas fee: {} tokens", expected_small_deltas_fee);
    println!("Rounding overhead: {} tokens", small_deltas_fee_collected - baseline_fee_collected);

    assert_eq!(small_deltas_fee_collected, expected_small_deltas_fee);
    assert!(small_deltas_fee_collected > baseline_fee_collected,
           "Small deltas should collect more fees due to rounding");

    // === SCENARIO C: Fee leakage through positive delta manipulation ===
    println!("\n=== SCENARIO C: Fee leakage through positive delta manipulation ===");

    // Setup: Give user1 tokens for the negative delta, and multiple helper accounts tokens
    const MANIPULATION_NEGATIVE_AMOUNT: u128 = 1;
    env.defuse_ft_deposit_to(&env.ft1, MANIPULATION_NEGATIVE_AMOUNT, env.user1.id()).await.unwrap();

    // Create 99 helper accounts with 100 tokens each
    const NUM_HELPERS: usize = 99;
    const HELPER_AMOUNT: u128 = 100;
    let mut helper_accounts = Vec::new();

    for i in 0..NUM_HELPERS {
        let helper = env.sandbox().create_account(&format!("helper{}", i)).await;
        env.defuse_ft_deposit_to(&env.ft1, HELPER_AMOUNT, helper.id()).await.unwrap();
        helper_accounts.push(helper);
    }

    // Create intents: 99 positive deltas (fee-free) + 1 small negative delta (fee-bearing)
    let mut manipulation_intents = Vec::new();

    // 99 positive deltas from helpers (fee-free)
    for (i, helper) in helper_accounts.iter().enumerate() {
        let intent = helper.sign_defuse_message(
            SigningStandard::default(),
            env.defuse.id(),
            rng.random(),
            Deadline::timeout(Duration::from_secs(120)),
            DefuseIntents {
                intents: [TokenDiff {
                    diff: TokenDeltas::new([(token_id.clone(), HELPER_AMOUNT as i128)].into_iter().collect()),
                    memo: Some(format!("positive_helper_{}", i)),
                    referral: None,
                }.into()].into(),
            },
        );
        manipulation_intents.push(intent);
    }

    // 1 small negative delta from user1 (the only fee-bearing transaction)
    let user1_negative_intent = env.user1.sign_defuse_message(
        SigningStandard::default(),
        env.defuse.id(),
        rng.random(),
        Deadline::timeout(Duration::from_secs(120)),
        DefuseIntents {
            intents: [TokenDiff {
                diff: TokenDeltas::new([(token_id.clone(), -(MANIPULATION_NEGATIVE_AMOUNT as i128))].into_iter().collect()),
                memo: Some("single_negative_user1".to_string()),
                referral: None,
            }.into()].into(),
        },
    );
    manipulation_intents.push(user1_negative_intent);

    // Add balancing negative delta to make the total net to zero
    // Total positive: 99 * 100 = 9,900
    // Total negative so far: 1
    // Need additional negative: 9,900 - 1 = 9,899
    const BALANCING_NEGATIVE: u128 = NUM_HELPERS as u128 * HELPER_AMOUNT - MANIPULATION_NEGATIVE_AMOUNT;
    env.defuse_ft_deposit_to(&env.ft1, BALANCING_NEGATIVE, env.user2.id()).await.unwrap();

    let balancing_negative_intent = env.user2.sign_defuse_message(
        SigningStandard::default(),
        env.defuse.id(),
        rng.random(),
        Deadline::timeout(Duration::from_secs(120)),
        DefuseIntents {
            intents: [TokenDiff {
                diff: TokenDeltas::new([(token_id.clone(), -(BALANCING_NEGATIVE as i128))].into_iter().collect()),
                memo: Some("balancing_negative".to_string()),
                referral: None,
            }.into()].into(),
        },
    );
    manipulation_intents.push(balancing_negative_intent);

    // Check fee collector balance before
    let fee_collector_balance_before_c = env.defuse
        .mt_balance_of(&fee_collector_id, &token_id.to_string())
        .await
        .unwrap();

    // Execute all manipulation intents
    env.defuse.execute_intents(manipulation_intents).await.unwrap();

    // Check fee collector balance after
    let fee_collector_balance_after_c = env.defuse
        .mt_balance_of(&fee_collector_id, &token_id.to_string())
        .await
        .unwrap();

    let manipulation_fee_collected = fee_collector_balance_after_c - fee_collector_balance_before_c;
    // Fee on -1 delta + fee on -9,899 delta
    let expected_manipulation_fee = fee.fee_ceil(MANIPULATION_NEGATIVE_AMOUNT) + fee.fee_ceil(BALANCING_NEGATIVE);
    let total_economic_flow = NUM_HELPERS as u128 * HELPER_AMOUNT + MANIPULATION_NEGATIVE_AMOUNT + BALANCING_NEGATIVE;
    let effective_fee_rate = (manipulation_fee_collected as f64) / (total_economic_flow as f64);
    let configured_fee_rate = fee.as_f64();

    // Calculate what the fee would be if charged on the full economic flow
    let hypothetical_full_fee = fee.fee_ceil(total_economic_flow);
    let fee_leakage_amount = hypothetical_full_fee - manipulation_fee_collected;

    println!("Manipulation fee collected: {} tokens", manipulation_fee_collected);
    println!("Expected manipulation fee: {} tokens", expected_manipulation_fee);
    println!("Total economic flow: {} tokens", total_economic_flow);
    println!("Hypothetical fee on full flow: {} tokens", hypothetical_full_fee);
    println!("Fee leakage amount: {} tokens", fee_leakage_amount);
    println!("Effective fee rate: {:.6}% vs configured: {:.6}%",
             effective_fee_rate * 100.0, configured_fee_rate * 100.0);

    assert_eq!(manipulation_fee_collected, expected_manipulation_fee);

    // The key insight: even though we moved 9,900 tokens through positive deltas (fee-free),
    // the system still charges fees on the negative deltas, but the economic impact is that
    // most of the volume flows through fee-free positive deltas

    // === SUMMARY ===
    println!("\n=== FEE LEAKAGE SUMMARY ===");
    println!("Scenario A (baseline): {} tokens fee on {} tokens flow",
             baseline_fee_collected, TOTAL_AMOUNT);
    println!("Scenario B (consolidation): {} tokens fee on {} tokens flow (+{} rounding overhead)",
             small_deltas_fee_collected, TOTAL_AMOUNT,
             small_deltas_fee_collected - baseline_fee_collected);
    println!("Scenario C (manipulation): {} tokens fee on {} tokens flow",
             manipulation_fee_collected, total_economic_flow);

    let baseline_rate = (baseline_fee_collected as f64) / (TOTAL_AMOUNT as f64);
    let small_deltas_rate = (small_deltas_fee_collected as f64) / (TOTAL_AMOUNT as f64);
    let manipulation_rate = (manipulation_fee_collected as f64) / (total_economic_flow as f64);

    println!("Baseline effective rate: {:.4}%", baseline_rate * 100.0);
    println!("Small deltas effective rate: {:.4}%", small_deltas_rate * 100.0);
    println!("Manipulation effective rate: {:.4}%", manipulation_rate * 100.0);

    // Key demonstrations:
    println!("\n=== KEY DEMONSTRATIONS ===");
    println!("1. Rounding overhead: {} extra tokens from fragmentation",
             small_deltas_fee_collected - baseline_fee_collected);
    println!("2. Fee-free positive deltas: {} tokens moved without fees",
             NUM_HELPERS as u128 * HELPER_AMOUNT);
    println!("3. Configured vs actual: {:.4}% configured, {:.4}% effective on full flow",
             configured_fee_rate * 100.0, manipulation_rate * 100.0);

    // Assertions to prove the fee leakage mechanisms
    assert!(small_deltas_fee_collected > baseline_fee_collected,
           "Many small deltas should incur more fees due to rounding");
    assert_eq!(manipulation_fee_collected, expected_manipulation_fee,
           "Manipulation scenario should only charge fees on negative deltas");

    // The core issue: positive deltas are fee-free, allowing volume to flow without fees
    println!("\n=== CORE ISSUE DEMONSTRATED ===");
    println!("✓ Positive deltas carry {} tokens fee-free", NUM_HELPERS as u128 * HELPER_AMOUNT);
    println!("✓ Only negative deltas pay fees: {} + {} tokens",
             MANIPULATION_NEGATIVE_AMOUNT, BALANCING_NEGATIVE);
    println!("✓ Economic impact: Large volume flows through fee-free positive deltas");
    println!("✓ Rounding amplifies the issue: {} vs {} tokens in fees",
             small_deltas_fee_collected, baseline_fee_collected);
}
