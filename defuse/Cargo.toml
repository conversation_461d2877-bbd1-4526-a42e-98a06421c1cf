[package]
name = "defuse"
version = "0.2.9"
edition.workspace = true
rust-version.workspace = true
repository.workspace = true
readme = "../README.md"

[lib]
crate-type = ["cdylib", "rlib"]

[lints]
workspace = true

[dependencies]
defuse-admin-utils.workspace = true
defuse-auth-call.workspace = true
defuse-bitmap = { workspace = true, optional = true }
defuse-borsh-utils = { workspace = true, optional = true }
defuse-controller.workspace = true
defuse-core.workspace = true
defuse-io-utils = { workspace = true, optional = true }
defuse-near-utils.workspace = true
defuse-nep245.workspace = true
defuse-map-utils = { workspace = true, optional = true }
defuse-serde-utils.workspace = true
defuse-wnear = { workspace = true, optional = true }

bitflags = { workspace = true, optional = true }
bnum.workspace = true
impl-tools.workspace = true
near-account-id.workspace = true
near-contract-standards.workspace = true
near-plugins.workspace = true
near-sdk.workspace = true
serde_with.workspace = true
strum.workspace = true
thiserror.workspace = true

[features]
abi = ["defuse-core/abi"]
contract = [
    "dep:defuse-bitmap",
    "dep:defuse-borsh-utils",
    "dep:defuse-io-utils",
    "dep:defuse-map-utils",
    "dep:defuse-wnear",
    "dep:bitflags",
]

[dev-dependencies]
defuse-core = { workspace = true, features = ["arbitrary"] }
defuse-test-utils.workspace = true

arbitrary_with.workspace = true
itertools.workspace = true
rstest.workspace = true
near-sdk = { workspace = true, features = ["unit-testing"] }
